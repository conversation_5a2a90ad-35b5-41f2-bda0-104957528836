// 應用狀態管理服務
// 統一處理應用程式的複雜狀態邏輯

const AppStateService = {
  // 語言相關狀態管理
  language: {
    // 儲存語言偏好
    saveLanguagePreference: (language) => {
      try {
        localStorage.setItem('selectedLanguage', language);
        console.log('💾 語言偏好已儲存:', language);
      } catch (error) {
        console.warn('⚠️ 無法儲存語言偏好:', error);
      }
    },

    // 載入語言偏好
    loadLanguagePreference: () => {
      try {
        return localStorage.getItem('selectedLanguage') || 'zh';
      } catch (error) {
        console.warn('⚠️ 無法載入語言偏好:', error);
        return 'zh';
      }
    }
  },

  // 位置相關狀態管理
  location: {
    // 儲存位置到本地存儲
    saveLocationToStorage: (location, name = null) => {
      try {
        const savedLocations = AppStateService.location.getSavedLocations();
        const locationData = {
          ...location,
          name: name || '未命名位置',
          timestamp: Date.now()
        };
        
        savedLocations.push(locationData);
        localStorage.setItem('savedLocations', JSON.stringify(savedLocations));
        console.log('💾 位置已儲存:', locationData);
        return locationData;
      } catch (error) {
        console.warn('⚠️ 無法儲存位置:', error);
        return null;
      }
    },

    // 獲取已儲存的位置
    getSavedLocations: () => {
      try {
        const saved = localStorage.getItem('savedLocations');
        return saved ? JSON.parse(saved) : [];
      } catch (error) {
        console.warn('⚠️ 無法載入已儲存位置:', error);
        return [];
      }
    }
  },

  // 應用程式初始化狀態
  initialization: {
    // 檢查是否為首次載入
    isFirstLoad: () => {
      return !localStorage.getItem('hasLoaded');
    },

    // 標記應用程式已載入
    markAsLoaded: () => {
      localStorage.setItem('hasLoaded', 'true');
    }
  },

  // 錯誤狀態管理
  error: {
    // 記錄錯誤到本地存儲（用於調試）
    logError: (error, context = '') => {
      const errorLog = {
        error: error.message || error,
        context,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent
      };
      
      console.error('❌ 應用程式錯誤:', errorLog);
      
      // 可以在這裡添加錯誤上報邏輯
    }
  }
};

// 導出為全域對象
window.AppStateService = AppStateService;