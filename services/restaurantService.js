// 餐廳相關業務邏輯服務
// 將餐廳搜索、過濾等邏輯從UI組件中分離

const RestaurantService = {
  // 過濾餐廳的核心邏輯
  filterRestaurants: (restaurants, searchRadius, mealTime = null) => {
    if (!restaurants || restaurants.length === 0) return [];
    
    let filtered = restaurants.filter(restaurant => {
      // 基本過濾條件
      if (!restaurant || !restaurant.name) return false;
      
      // 距離過濾（如果有距離資訊）
      if (restaurant.distance && restaurant.distance > searchRadius * 1000) {
        return false;
      }
      
      return true;
    });
    
    // 餐點時間過濾
    if (mealTime && window.filterByMealTime) {
      filtered = window.filterByMealTime(filtered, mealTime);
    }
    
    return filtered;
  },

  // 隨機選擇餐廳
  getRandomRestaurant: (restaurants) => {
    if (!restaurants || restaurants.length === 0) return null;
    const randomIndex = Math.floor(Math.random() * restaurants.length);
    return restaurants[randomIndex];
  },

  // 搜索餐廳的統一接口
  searchRestaurants: async (location, radius, mealTime = null) => {
    try {
      // 使用現有的全域搜索函數
      if (window.searchNearbyRestaurants) {
        const results = await window.searchNearbyRestaurants(location.lat, location.lng, radius);
        return RestaurantService.filterRestaurants(results, radius, mealTime);
      }
      return [];
    } catch (error) {
      console.error('❌ 餐廳搜索失敗:', error);
      return [];
    }
  },

  // 餐廳資訊驗證
  validateRestaurant: (restaurant) => {
    if (!restaurant) return false;
    return !!(restaurant.name && (restaurant.vicinity || restaurant.formatted_address));
  },

  // 餐廳資料標準化
  normalizeRestaurant: (restaurant) => {
    if (!RestaurantService.validateRestaurant(restaurant)) return null;
    
    return {
      ...restaurant,
      address: restaurant.vicinity || restaurant.formatted_address || '地址未提供',
      rating: restaurant.rating || 0,
      distance: restaurant.distance || null
    };
  }
};

// 導出為全域對象以維持架構相容性
window.RestaurantService = RestaurantService;